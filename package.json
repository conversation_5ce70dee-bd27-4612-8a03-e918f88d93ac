{"name": "borouge-esg-platform", "version": "1.0.0", "description": "Borouge ESG Intelligence Platform - Frontend Application", "private": true, "author": "Borouge ESG Intelligence Team", "license": "MIT", "keywords": ["esg", "intelligence", "borouge", "petrochemical", "sustainability", "react"], "homepage": ".", "dependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "framer-motion": "^12.15.0", "lucide-react": "^0.511.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-scripts": "5.0.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test --watchAll=false", "test:watch": "react-scripts test", "test:coverage": "react-scripts test --coverage --watchAll=false", "eject": "react-scripts eject", "lint": "eslint src/**/*.{js,jsx}", "lint:fix": "eslint src/**/*.{js,jsx} --fix", "format": "prettier --write src/**/*.{js,jsx,css,md}", "analyze": "npm run build && npx bundle-analyzer build/static/js/*.js", "clean": "rm -rf build node_modules package-lock.json && npm install"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"], "rules": {"no-console": "warn", "no-unused-vars": "warn", "react/prop-types": "warn"}}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all", "not ie <= 11"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "jest": {"collectCoverageFrom": ["src/**/*.{js,jsx}", "!src/index.js", "!src/reportWebVitals.js"], "coverageThreshold": {"global": {"branches": 70, "functions": 70, "lines": 70, "statements": 70}}}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}