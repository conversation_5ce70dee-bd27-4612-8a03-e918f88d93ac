{"name": "borouge-esg-backend", "version": "1.0.0", "description": "Production-ready backend for Borouge ESG Intelligence Platform with multi-AI integration", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest --watchAll=false", "test:watch": "jest --watch", "test:coverage": "jest --coverage --watchAll=false", "lint": "eslint **/*.js", "lint:fix": "eslint **/*.js --fix", "format": "prettier --write **/*.{js,json,md}", "health": "curl -f http://localhost:3001/health || exit 1", "clean": "rm -rf node_modules package-lock.json && npm install", "docker:build": "docker build -t borouge-esg-backend .", "docker:run": "docker run -p 3001:3001 borouge-esg-backend"}, "keywords": ["esg", "intelligence", "borouge", "petrochemical", "sustainability", "api", "express", "supabase", "ai", "groq", "gemini", "openai"], "author": "Borouge ESG Intelligence Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/Mitty530/Borouge.git"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "dependencies": {"@supabase/supabase-js": "^2.49.8", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0"}, "devDependencies": {"nodemon": "^3.1.10", "jest": "^29.7.0", "eslint": "^8.57.0", "prettier": "^3.2.5", "supertest": "^6.3.4"}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["**/*.js", "!node_modules/**", "!coverage/**"], "coverageThreshold": {"global": {"branches": 70, "functions": 70, "lines": 70, "statements": 70}}}, "eslintConfig": {"env": {"node": true, "es2021": true, "jest": true}, "extends": "eslint:recommended", "rules": {"no-console": "off", "no-unused-vars": "warn"}}}