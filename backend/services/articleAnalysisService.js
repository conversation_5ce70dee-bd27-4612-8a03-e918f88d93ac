// Article Analysis Service for Borouge ESG Intelligence Platform
// AI-powered analysis of news articles for relevance and business impact

const crypto = require('crypto');

class ArticleAnalysisService {
  constructor(config, supabase, aiService) {
    this.config = config;
    this.supabase = supabase;
    this.aiService = aiService;
    
    console.log('🤖 Article Analysis Service initialized');
  }

  // Generate query hash for caching
  generateQueryHash(query) {
    return crypto.createHash('sha256').update(query.toLowerCase().trim()).digest('base64');
  }

  // Analyze articles with AI for relevance and impact
  async analyzeArticlesWithAI(articles, queryEnhancements) {
    const analyzedArticles = [];
    
    for (const article of articles) {
      try {
        // Create analysis prompt for this article
        const analysisPrompt = this.createArticleAnalysisPrompt(article, queryEnhancements);
        
        // Use existing AI service to analyze
        const aiAnalysis = await this.aiService.analyzeQuery(analysisPrompt);
        
        // Parse AI response to extract relevance score and impact level
        const analysis = this.parseArticleAnalysis(aiAnalysis.response);
        
        // Update article in database with analysis
        await this.updateArticleAnalysis(article.id, analysis);
        
        // Add to analyzed articles
        analyzedArticles.push({
          ...article,
          ...analysis
        });
        
        console.log(`🤖 Analyzed article: ${article.title.substring(0, 50)}... (Score: ${analysis.relevance_score})`);
        
      } catch (error) {
        console.error(`❌ Error analyzing article ${article.id}:`, error.message);
        
        // Add article with default analysis
        analyzedArticles.push({
          ...article,
          relevance_score: 50,
          impact_level: 'LOW',
          summary: article.description || 'No summary available',
          action_items: []
        });
      }
    }
    
    return analyzedArticles;
  }

  // Create analysis prompt for article
  createArticleAnalysisPrompt(article, queryEnhancements) {
    return `As Borouge's Senior ESG Intelligence Analyst, analyze this article for relevance to our operations:

ARTICLE:
Title: ${article.title}
Description: ${article.description}
Source: ${article.source}
Published: ${article.published_at}

SEARCH CONTEXT:
Original Query: ${queryEnhancements.originalQuery}
Enhanced Keywords: ${queryEnhancements.enhancedKeywords.join(', ')}
Priority Level: ${queryEnhancements.priorityLevel}

BOROUGE CONTEXT:
- Company: Joint venture (ADNOC 54%, Borealis 36%, Public 10%)
- Products: Polyethylene (LLDPE, HDPE), Polypropylene (PP)
- Markets: UAE, Singapore, China, India, Europe, Asia
- Revenue: $8.5B annually, €2.3B EU exposure (27%)
- Operations: Ruwais Industrial Complex, UAE (5.0M tonnes capacity)

ANALYSIS REQUIRED:
1. Relevance Score (0-100): How relevant is this to Borouge's petrochemical operations?
2. Impact Level: HIGH (immediate action needed), MEDIUM (strategic planning), LOW (monitoring), OPPORTUNITY (competitive advantage)
3. Summary: 2-3 sentence executive summary focused on Borouge implications
4. Action Items: Specific next steps for Borouge (if any)

Consider these factors:
- Regulatory changes affecting petrochemicals/plastics
- Market developments in key regions
- Competitive intelligence on SABIC, Dow, ExxonMobil
- ESG/sustainability trends impacting operations
- Supply chain or trade policy changes

Respond in JSON format:
{
  "relevance_score": 85,
  "impact_level": "HIGH",
  "summary": "Brief executive summary focused on Borouge implications...",
  "action_items": ["Specific action 1", "Specific action 2"]
}`;
  }

  // Parse AI analysis response
  parseArticleAnalysis(aiResponse) {
    try {
      // Try to extract JSON from AI response
      const jsonMatch = aiResponse.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const analysis = JSON.parse(jsonMatch[0]);
        return {
          relevance_score: Math.min(100, Math.max(0, analysis.relevance_score || 50)),
          impact_level: ['HIGH', 'MEDIUM', 'LOW', 'OPPORTUNITY'].includes(analysis.impact_level) 
            ? analysis.impact_level : 'LOW',
          summary: analysis.summary || 'No summary available',
          action_items: Array.isArray(analysis.action_items) ? analysis.action_items : []
        };
      }
    } catch (error) {
      console.error('❌ Error parsing AI analysis:', error.message);
    }
    
    // Fallback analysis
    return {
      relevance_score: 50,
      impact_level: 'LOW',
      summary: 'Analysis not available',
      action_items: []
    };
  }

  // Update article analysis in database
  async updateArticleAnalysis(articleId, analysis) {
    try {
      await this.supabase
        .from('esg_news_articles')
        .update({
          relevance_score: analysis.relevance_score,
          impact_level: analysis.impact_level,
          summary: analysis.summary,
          action_items: analysis.action_items,
          processed_at: new Date().toISOString(),
          processing_status: 'completed'
        })
        .eq('id', articleId);
    } catch (error) {
      console.error('❌ Error updating article analysis:', error.message);
    }
  }

  // Structure smart search response
  structureSmartSearchResponse(query, queryEnhancements, newsResults, analyzedArticles, startTime) {
    const responseTime = Date.now() - startTime;
    
    // Sort articles by relevance and impact
    const sortedArticles = analyzedArticles
      .filter(a => a.relevance_score >= 60) // Only include relevant articles
      .sort((a, b) => {
        // Sort by impact level first, then relevance score
        const impactOrder = { 'HIGH': 4, 'OPPORTUNITY': 3, 'MEDIUM': 2, 'LOW': 1 };
        const impactDiff = (impactOrder[b.impact_level] || 1) - (impactOrder[a.impact_level] || 1);
        if (impactDiff !== 0) return impactDiff;
        return (b.relevance_score || 0) - (a.relevance_score || 0);
      });

    // Generate executive summary
    const highImpactCount = analyzedArticles.filter(a => a.impact_level === 'HIGH').length;
    const opportunityCount = analyzedArticles.filter(a => a.impact_level === 'OPPORTUNITY').length;
    const relevantCount = analyzedArticles.filter(a => a.relevance_score >= 70).length;

    const executiveSummary = `Found ${newsResults.articlesFound} articles, ${relevantCount} highly relevant to Borouge operations. ${highImpactCount} require immediate attention, ${opportunityCount} present strategic opportunities.`;

    return {
      success: true,
      timestamp: new Date().toISOString(),
      query: query,
      responseTime: responseTime,
      cached: false,
      
      // Search metadata
      searchMetadata: {
        originalQuery: query,
        enhancedKeywords: queryEnhancements.enhancedKeywords,
        searchStrategies: queryEnhancements.searchStrategies,
        priorityLevel: queryEnhancements.priorityLevel
      },
      
      // Results summary
      resultsSummary: {
        totalArticles: newsResults.articlesFound,
        relevantArticles: relevantCount,
        highImpactCount: highImpactCount,
        mediumImpactCount: analyzedArticles.filter(a => a.impact_level === 'MEDIUM').length,
        lowImpactCount: analyzedArticles.filter(a => a.impact_level === 'LOW').length,
        opportunityCount: opportunityCount,
        executiveSummary: executiveSummary
      },
      
      // Processed articles
      articles: sortedArticles.slice(0, 15), // Limit to top 15 articles
      
      // Action items from high-impact articles
      actionItems: this.extractActionItems(sortedArticles.filter(a => a.impact_level === 'HIGH')),
      
      // API usage info
      apiUsage: {
        quotaRemaining: newsResults.quotaRemaining,
        provider: 'gnews'
      }
    };
  }

  // Extract action items from high-impact articles
  extractActionItems(highImpactArticles) {
    const actionItems = [];
    
    highImpactArticles.forEach(article => {
      if (article.action_items && Array.isArray(article.action_items)) {
        article.action_items.forEach(action => {
          actionItems.push({
            action: action,
            source: article.title,
            url: article.url,
            priority: article.impact_level,
            publishedAt: article.published_at
          });
        });
      }
    });
    
    return actionItems.slice(0, 10); // Limit to top 10 action items
  }

  // Get analytics for article analysis performance
  async getAnalysisAnalytics(days = 7) {
    try {
      const { data: analytics, error } = await this.supabase
        .from('esg_news_articles')
        .select('impact_level, relevance_score, processing_status, created_at')
        .gte('created_at', new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString())
        .eq('processing_status', 'completed');

      if (error) {
        throw error;
      }

      // Calculate analytics
      const totalAnalyzed = analytics.length;
      const avgRelevanceScore = analytics.reduce((sum, a) => sum + (a.relevance_score || 0), 0) / totalAnalyzed;
      const impactDistribution = {
        HIGH: analytics.filter(a => a.impact_level === 'HIGH').length,
        MEDIUM: analytics.filter(a => a.impact_level === 'MEDIUM').length,
        LOW: analytics.filter(a => a.impact_level === 'LOW').length,
        OPPORTUNITY: analytics.filter(a => a.impact_level === 'OPPORTUNITY').length
      };

      return {
        totalAnalyzed,
        avgRelevanceScore: Math.round(avgRelevanceScore),
        impactDistribution,
        period: `${days} days`
      };

    } catch (error) {
      console.error('❌ Error fetching analysis analytics:', error.message);
      return null;
    }
  }
}

module.exports = ArticleAnalysisService;
