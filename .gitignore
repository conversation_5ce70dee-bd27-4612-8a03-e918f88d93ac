# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# Dependencies
node_modules/
/.pnp
.pnp.js
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# testing
/coverage

# production
/build

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
node_modules/
jspm_packages/

# TypeScript v1 declaration files
typings/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.test

# parcel-bundler cache
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# React build output
build/

# Backend specific
backend/uploads/
backend/logs/

# Database
*.db
*.sqlite
*.sqlite3

# API Keys and sensitive data (double protection)
**/config/keys.js
**/config/production.js
**/.env
**/.env.*
!**/.env.example
**/APIs
backend/APIs

# Cache files
.cache/
*.cache

# Lock files (keep package-lock.json but ignore others)
yarn.lock

# Test coverage
coverage/
.nyc_output/

# Backup files
*.backup
*.bak
*.tmp

# IDE files
*.sublime-project
*.sublime-workspace

# MacOS
.AppleDouble
.LSOverride

# Windows
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ESG Intelligence Platform Specific
# AI Provider cache directories
.groq_cache/
.gemini_cache/
.openai_cache/

# ESG data and reports
esg_reports/
intelligence_cache/
analytics_data/

# Supabase local development
.supabase/
supabase/.branches
supabase/.temp

# Docker
.dockerignore
Dockerfile
docker-compose.yml
docker-compose.override.yml

# Deployment
.vercel/
.netlify/

# Performance monitoring
.clinic/
clinic.json

# Memory dumps
*.heapsnapshot

# Certificate files
*.crt
*.cert
*.ca-bundle
*.key
*.pem
*.p12
*.pfx

# Archive files
*.zip
*.rar
*.7z

# AI models (if any)
*.model
*.pkl
*.h5
*.pb

# Local configuration overrides
config.local.js
config.local.json

# Package manager alternatives
pnpm-lock.yaml
.pnpm-debug.log*
