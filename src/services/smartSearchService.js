// Smart Search Service for ESG Intelligence Engine
// Provides mock data for demonstration purposes

const mockArticles = [
  {
    title: "EU Plastic Waste Regulations: New Framework for 2024",
    description: "The European Union introduces comprehensive plastic waste regulations affecting petrochemical companies globally, with specific implications for Middle Eastern producers.",
    source: { name: "Reuters" },
    publishedAt: "2024-01-15T10:30:00Z",
    url: "https://www.reuters.com/business/environment/",
    impact_level: "HIGH",
    relevance_score: 95
  },
  {
    title: "CBAM Implementation: Carbon Border Adjustment Impact on Petrochemicals",
    description: "Analysis of how the Carbon Border Adjustment Mechanism will affect petrochemical exports from the Middle East to European markets.",
    source: { name: "Financial Times" },
    publishedAt: "2024-01-12T14:20:00Z",
    url: "https://www.ft.com/climate-capital",
    impact_level: "HIGH",
    relevance_score: 92
  },
  {
    title: "Circular Economy Initiatives in Plastic Manufacturing",
    description: "Leading petrochemical companies are investing in circular economy solutions to meet sustainability targets and regulatory requirements.",
    source: { name: "Chemical Week" },
    publishedAt: "2024-01-10T09:15:00Z",
    url: "https://www.chemweek.com/",
    impact_level: "MEDIUM",
    relevance_score: 88
  },
  {
    title: "SABIC Announces Major Sustainability Investment",
    description: "SABIC commits $2 billion to sustainable technology development, setting new industry benchmarks for ESG performance.",
    source: { name: "Arabian Business" },
    publishedAt: "2024-01-08T11:45:00Z",
    url: "https://www.arabianbusiness.com/industries/energy",
    impact_level: "OPPORTUNITY",
    relevance_score: 85
  },
  {
    title: "Renewable Feedstock Adoption in Petrochemical Industry",
    description: "Industry analysis shows accelerating adoption of renewable feedstocks as companies prepare for stricter environmental regulations.",
    source: { name: "ICIS" },
    publishedAt: "2024-01-05T16:30:00Z",
    url: "https://www.icis.com/explore/commodities/chemicals/",
    impact_level: "MEDIUM",
    relevance_score: 82
  }
];

const generateMockResponse = (query) => {
  // Filter articles based on query relevance
  const relevantArticles = mockArticles.filter(article => {
    const queryLower = query.toLowerCase();
    return (
      article.title.toLowerCase().includes(queryLower) ||
      article.description.toLowerCase().includes(queryLower) ||
      queryLower.split(' ').some(word =>
        article.title.toLowerCase().includes(word) ||
        article.description.toLowerCase().includes(word)
      )
    );
  });

  // If no specific matches, return all articles
  const articles = relevantArticles.length > 0 ? relevantArticles : mockArticles;

  // Generate summary based on query
  const summary = generateSummary(query, articles);

  // Generate analysis
  const analysis = generateAnalysis(query, articles);

  // Generate enhanced keywords
  const enhancedKeywords = generateEnhancedKeywords(query);

  // Generate action items
  const actionItems = generateActionItems(query, articles);

  // Generate statistics
  const statistics = generateStatistics(articles);

  return {
    summary,
    articles,
    analysis,
    enhancedKeywords,
    actionItems,
    statistics,
    metadata: {
      query,
      totalArticles: articles.length,
      processingTime: Math.floor(Math.random() * 500) + 200,
      timestamp: new Date().toISOString()
    }
  };
};

const generateSummary = (query, articles) => {
  const highImpactCount = articles.filter(a => a.impact_level === 'HIGH').length;
  const opportunityCount = articles.filter(a => a.impact_level === 'OPPORTUNITY').length;

  if (query.toLowerCase().includes('regulation') || query.toLowerCase().includes('eu')) {
    return `Analysis of ${articles.length} articles reveals significant regulatory developments affecting Borouge operations. ${highImpactCount} high-impact regulatory changes identified, requiring immediate strategic response. Key focus areas include compliance preparation, supply chain adjustments, and market positioning strategies.`;
  }

  if (query.toLowerCase().includes('cbam') || query.toLowerCase().includes('carbon')) {
    return `Carbon Border Adjustment Mechanism (CBAM) analysis shows critical implications for Borouge's European market strategy. ${articles.length} relevant developments tracked, with ${highImpactCount} requiring immediate action. Focus on carbon accounting, supply chain optimization, and competitive positioning needed.`;
  }

  if (query.toLowerCase().includes('circular') || query.toLowerCase().includes('sustainability')) {
    return `Circular economy trends analysis reveals ${opportunityCount} strategic opportunities for Borouge. Market shift toward sustainable solutions creates competitive advantages for early adopters. Investment in recycling technologies and sustainable product lines recommended.`;
  }

  return `Comprehensive ESG intelligence analysis of ${articles.length} articles reveals ${highImpactCount} high-impact developments and ${opportunityCount} strategic opportunities. Immediate attention required for regulatory compliance and market positioning strategies.`;
};

const generateAnalysis = (query, articles) => {
  return {
    riskLevel: articles.some(a => a.impact_level === 'HIGH') ? 'High' : 'Medium',
    opportunityScore: articles.filter(a => a.impact_level === 'OPPORTUNITY').length * 20,
    timeframe: 'Next 6-12 months',
    strategicImportance: 'Critical for market positioning',
    recommendedActions: [
      'Conduct detailed regulatory impact assessment',
      'Engage with industry associations and regulators',
      'Develop compliance roadmap and timeline',
      'Assess competitive positioning and market opportunities'
    ]
  };
};

const generateEnhancedKeywords = (query) => {
  const baseKeywords = query.toLowerCase().split(' ');
  const enhancedKeywords = [...baseKeywords];

  // Add Borouge-specific context
  if (query.toLowerCase().includes('regulation') || query.toLowerCase().includes('eu')) {
    enhancedKeywords.push('Borouge compliance', 'petrochemical regulations', 'Middle East exports');
  }

  if (query.toLowerCase().includes('carbon') || query.toLowerCase().includes('cbam')) {
    enhancedKeywords.push('carbon accounting', 'CBAM compliance', 'UAE carbon strategy');
  }

  if (query.toLowerCase().includes('plastic') || query.toLowerCase().includes('waste')) {
    enhancedKeywords.push('circular economy', 'plastic recycling', 'sustainable polymers');
  }

  // Add industry-specific terms
  enhancedKeywords.push('ESG compliance', 'sustainability reporting', 'regulatory impact');

  return [...new Set(enhancedKeywords)]; // Remove duplicates
};

const generateActionItems = (query, articles) => {
  const actionItems = [];

  if (query.toLowerCase().includes('regulation') || query.toLowerCase().includes('eu')) {
    actionItems.push({
      action: 'Schedule regulatory compliance review meeting with legal team',
      priority: 'HIGH',
      timeframe: 'Within 2 weeks',
      department: 'Legal & Compliance'
    });
    actionItems.push({
      action: 'Assess impact on current product portfolio and supply chain',
      priority: 'HIGH',
      timeframe: 'Within 1 month',
      department: 'Operations'
    });
  }

  if (query.toLowerCase().includes('carbon') || query.toLowerCase().includes('cbam')) {
    actionItems.push({
      action: 'Implement carbon tracking system for EU-bound shipments',
      priority: 'CRITICAL',
      timeframe: 'Within 3 months',
      department: 'Supply Chain'
    });
  }

  // Default action items
  if (actionItems.length === 0) {
    actionItems.push({
      action: 'Monitor regulatory developments and industry trends',
      priority: 'MEDIUM',
      timeframe: 'Ongoing',
      department: 'Strategy'
    });
  }

  return actionItems;
};

const generateStatistics = (articles) => {
  const highImpactCount = articles.filter(a => a.impact_level === 'HIGH').length;
  const opportunityCount = articles.filter(a => a.impact_level === 'OPPORTUNITY').length;
  const mediumImpactCount = articles.filter(a => a.impact_level === 'MEDIUM').length;

  return {
    totalArticles: articles.length,
    relevantArticles: articles.length,
    highImpactCount,
    mediumImpactCount,
    opportunityCount,
    averageRelevance: Math.round(articles.reduce((sum, a) => sum + a.relevance_score, 0) / articles.length)
  };
};

// Try to fetch real data from backend, fallback to mock data
const fetchRealData = async (query) => {
  try {
    const response = await fetch('http://localhost:3001/api/esg-smart-search', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ query }),
    });

    if (response.ok) {
      const data = await response.json();
      console.log('✅ Using real backend data');
      return data;
    } else {
      console.log('⚠️ Backend unavailable, using mock data');
      return null;
    }
  } catch (error) {
    console.log('⚠️ Backend connection failed, using mock data:', error.message);
    return null;
  }
};

export const smartSearchService = {
  search: async (query) => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1500));

    try {
      // Try to get real data first
      const realData = await fetchRealData(query);

      if (realData && realData.success) {
        // Transform backend response to match our expected format
        return {
          summary: realData.summary || `Analysis of ${realData.articles?.length || 0} articles for: ${query}`,
          articles: realData.articles || [],
          analysis: realData.analysis || generateAnalysis(query, realData.articles || []),
          enhancedKeywords: realData.searchMetadata?.enhancedKeywords || generateEnhancedKeywords(query),
          actionItems: realData.searchMetadata?.actionItems || generateActionItems(query, realData.articles || []),
          statistics: realData.searchMetadata?.statistics || generateStatistics(realData.articles || []),
          metadata: {
            query,
            totalArticles: realData.articles?.length || 0,
            processingTime: realData.processingTime || 500,
            timestamp: new Date().toISOString(),
            source: 'backend'
          }
        };
      }

      // Fallback to mock data
      console.log('📊 Using enhanced mock data');
      const response = generateMockResponse(query);
      response.metadata.source = 'mock';
      return response;
    } catch (error) {
      throw new Error('Smart search service unavailable');
    }
  }
};

export default smartSearchService;
