/* Smart Search Demo Styles for Borouge ESG Intelligence Platform */

.smart-search-demo {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Header */
.search-header {
  text-align: center;
  margin-bottom: 30px;
}

.search-header h2 {
  color: #2c3e50;
  margin-bottom: 10px;
  font-size: 2.2em;
}

.search-header p {
  color: #7f8c8d;
  font-size: 1.1em;
}

/* Search Input Section */
.search-input-section {
  margin-bottom: 30px;
}

.search-input-container {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

.search-input {
  flex: 1;
  padding: 15px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: #3498db;
}

.search-button {
  padding: 15px 25px;
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 140px;
}

.search-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #2980b9, #1f5f8b);
  transform: translateY(-2px);
}

.search-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Example Queries */
.example-queries {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.example-queries span {
  color: #7f8c8d;
  font-weight: 500;
  margin-right: 10px;
}

.example-query-button {
  padding: 6px 12px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 20px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.example-query-button:hover {
  background: #e9ecef;
  transform: translateY(-1px);
}

/* Error Message */
.error-message {
  background: #fff5f5;
  border: 1px solid #fed7d7;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.error-message h3 {
  color: #e53e3e;
  margin-bottom: 10px;
}

.error-help {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #fed7d7;
}

.error-help ul {
  margin: 10px 0;
  padding-left: 20px;
}

/* Loading State */
.loading-state {
  text-align: center;
  padding: 40px;
  background: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 20px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e9ecef;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-steps {
  margin-top: 20px;
}

.loading-steps div {
  margin: 8px 0;
  color: #6c757d;
  font-size: 14px;
}

/* Results Summary */
.results-summary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 25px;
  border-radius: 12px;
  margin-bottom: 25px;
}

.summary-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 20px;
  margin: 20px 0;
}

.stat {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 2.5em;
  font-weight: bold;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 0.9em;
  opacity: 0.9;
}

.executive-summary {
  font-size: 1.1em;
  line-height: 1.6;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.3);
}

/* Enhanced Keywords */
.enhanced-keywords {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 25px;
}

.keywords-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 10px;
}

.keyword-tag {
  background: #e3f2fd;
  color: #1976d2;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 14px;
  font-weight: 500;
}

/* Action Items */
.action-items {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 25px;
}

.action-item {
  background: white;
  padding: 15px;
  border-radius: 6px;
  margin-bottom: 10px;
  border-left: 4px solid #f39c12;
}

.action-text {
  font-weight: 500;
  margin-bottom: 8px;
}

.action-source {
  font-size: 14px;
  color: #6c757d;
}

.action-source a {
  color: #3498db;
  text-decoration: none;
}

/* Articles Section */
.articles-section h4 {
  color: #2c3e50;
  margin-bottom: 20px;
}

.article-card {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 15px;
  transition: all 0.3s ease;
}

.article-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.article-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.article-title {
  flex: 1;
  margin: 0 15px 0 0;
}

.article-title a {
  color: #2c3e50;
  text-decoration: none;
  font-size: 1.1em;
}

.article-title a:hover {
  color: #3498db;
}

.article-meta {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: flex-end;
}

/* Impact Badges */
.impact-badge {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
  text-transform: uppercase;
}

.impact-badge.high {
  background: #fee;
  color: #c53030;
}

.impact-badge.medium {
  background: #fff3cd;
  color: #b45309;
}

.impact-badge.low {
  background: #f0f9ff;
  color: #1e40af;
}

.impact-badge.opportunity {
  background: #f0fff4;
  color: #22543d;
}

.relevance-score {
  font-size: 12px;
  color: #6c757d;
  font-weight: 500;
}

.article-summary {
  color: #495057;
  line-height: 1.6;
  margin-bottom: 15px;
}

.article-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  color: #6c757d;
  padding-top: 15px;
  border-top: 1px solid #e9ecef;
}

/* API Usage */
.api-usage {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 6px;
  margin-top: 25px;
  font-size: 14px;
  color: #6c757d;
}

/* Responsive Design */
@media (max-width: 768px) {
  .search-input-container {
    flex-direction: column;
  }
  
  .summary-stats {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .article-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .article-meta {
    flex-direction: row;
    align-items: center;
    margin-top: 10px;
  }
}
