// Smart Search Demo Component for Borouge ESG Intelligence Platform
// Demonstrates the new ESG Smart Search functionality with news intelligence

import React, { useState } from 'react';
import './SmartSearchDemo.css';

const SmartSearchDemo = ({ onBack }) => {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Example queries for demonstration
  const exampleQueries = [
    'plastic waste regulations UAE',
    'CBAM impact petrochemical industry',
    'circular economy polyethylene',
    'REACH compliance polypropylene',
    'carbon border adjustment ADNOC'
  ];

  const handleSearch = async () => {
    if (!query.trim()) return;

    setLoading(true);
    setError(null);
    setResults(null);

    try {
      const response = await fetch('http://localhost:3001/api/esg-smart-search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ query: query.trim() })
      });

      const data = await response.json();

      if (data.success) {
        setResults(data);
      } else {
        setError(data.error?.message || 'Search failed');
      }
    } catch (err) {
      setError('Network error: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleExampleClick = (exampleQuery) => {
    setQuery(exampleQuery);
  };

  const getImpactBadgeClass = (level) => {
    switch (level) {
      case 'HIGH': return 'impact-badge high';
      case 'MEDIUM': return 'impact-badge medium';
      case 'LOW': return 'impact-badge low';
      case 'OPPORTUNITY': return 'impact-badge opportunity';
      default: return 'impact-badge';
    }
  };

  return (
    <div className="smart-search-demo">
      <div className="search-header">
        {onBack && (
          <button onClick={onBack} className="back-button">
            ← Back to Main
          </button>
        )}
        <h2>🔍 ESG Smart Search</h2>
        <p>Intelligent news search with Borouge-specific analysis</p>
      </div>

      {/* Search Input */}
      <div className="search-input-section">
        <div className="search-input-container">
          <input
            type="text"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            placeholder="Enter your ESG query (e.g., plastic waste regulations)"
            className="search-input"
            onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
          />
          <button
            onClick={handleSearch}
            disabled={loading || !query.trim()}
            className="search-button"
          >
            {loading ? '🔄 Searching...' : '🔍 Search'}
          </button>
        </div>

        {/* Example Queries */}
        <div className="example-queries">
          <span>Try these examples:</span>
          {exampleQueries.map((example, index) => (
            <button
              key={index}
              onClick={() => handleExampleClick(example)}
              className="example-query-button"
            >
              {example}
            </button>
          ))}
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="error-message">
          <h3>❌ Error</h3>
          <p>{error}</p>
          <div className="error-help">
            <p><strong>Note:</strong> This demo requires:</p>
            <ul>
              <li>✅ Backend server running on port 3001</li>
              <li>⚠️ GNews API key activated (currently needs activation)</li>
              <li>✅ Supabase database configured</li>
            </ul>
          </div>
        </div>
      )}

      {/* Loading State */}
      {loading && (
        <div className="loading-state">
          <div className="loading-spinner"></div>
          <p>Analyzing ESG intelligence...</p>
          <div className="loading-steps">
            <div>🎯 Enhancing query with Borouge context</div>
            <div>📰 Searching news sources</div>
            <div>🤖 AI analysis for relevance and impact</div>
            <div>📊 Generating actionable insights</div>
          </div>
        </div>
      )}

      {/* Results Display */}
      {results && (
        <div className="search-results">
          {/* Results Summary */}
          <div className="results-summary">
            <h3>📊 Search Results Summary</h3>
            <div className="summary-stats">
              <div className="stat">
                <span className="stat-number">{results.resultsSummary.totalArticles}</span>
                <span className="stat-label">Articles Found</span>
              </div>
              <div className="stat">
                <span className="stat-number">{results.resultsSummary.relevantArticles}</span>
                <span className="stat-label">Relevant</span>
              </div>
              <div className="stat">
                <span className="stat-number">{results.resultsSummary.highImpactCount}</span>
                <span className="stat-label">High Impact</span>
              </div>
              <div className="stat">
                <span className="stat-number">{results.resultsSummary.opportunityCount}</span>
                <span className="stat-label">Opportunities</span>
              </div>
            </div>
            <p className="executive-summary">{results.resultsSummary.executiveSummary}</p>
          </div>

          {/* Enhanced Keywords */}
          <div className="enhanced-keywords">
            <h4>🎯 Enhanced Search Terms</h4>
            <div className="keywords-list">
              {results.searchMetadata.enhancedKeywords.map((keyword, index) => (
                <span key={index} className="keyword-tag">{keyword}</span>
              ))}
            </div>
          </div>

          {/* Action Items */}
          {results.actionItems && results.actionItems.length > 0 && (
            <div className="action-items">
              <h4>⚡ Immediate Action Items</h4>
              {results.actionItems.map((item, index) => (
                <div key={index} className="action-item">
                  <div className="action-text">{item.action}</div>
                  <div className="action-source">
                    Source: <a href={item.url} target="_blank" rel="noopener noreferrer">
                      {item.source}
                    </a>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Articles */}
          <div className="articles-section">
            <h4>📰 Analyzed Articles</h4>
            {results.articles.map((article, index) => (
              <div key={index} className="article-card">
                <div className="article-header">
                  <h5 className="article-title">
                    <a href={article.url} target="_blank" rel="noopener noreferrer">
                      {article.title}
                    </a>
                  </h5>
                  <div className="article-meta">
                    <span className={getImpactBadgeClass(article.impact_level)}>
                      {article.impact_level}
                    </span>
                    <span className="relevance-score">
                      {article.relevance_score}% relevant
                    </span>
                  </div>
                </div>
                <p className="article-summary">{article.summary}</p>
                <div className="article-footer">
                  <span className="article-source">{article.source}</span>
                  <span className="article-date">
                    {new Date(article.published_at).toLocaleDateString()}
                  </span>
                </div>
              </div>
            ))}
          </div>

          {/* API Usage Info */}
          <div className="api-usage">
            <p>📊 API Usage: {results.apiUsage.quotaRemaining} requests remaining today</p>
            <p>⏱️ Processing time: {results.responseTime}ms</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default SmartSearchDemo;
