import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  ArrowLeft,
  Loader2
} from 'lucide-react';
import smartSearchService from '../services/smartSearchService';
import './ConversationView.css';

const ConversationView = ({ initialQuery, onBack }) => {
  const [messages, setMessages] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    if (initialQuery) {
      // Add initial user message
      const userMessage = {
        id: 1,
        type: 'user',
        content: initialQuery,
        timestamp: new Date()
      };

      setMessages([userMessage]);
      setIsLoading(true);

      // Call Smart Search API
      performSmartSearch(initialQuery);
    }
  }, [initialQuery]);

  const performSmartSearch = async (query) => {
    try {
      const data = await smartSearchService.search(query);

      const aiResponse = {
        id: 2,
        type: 'assistant',
        content: data,
        timestamp: new Date()
      };

      setMessages(prev => [...prev, aiResponse]);
      setIsLoading(false);
    } catch (error) {
      console.error('Smart search error:', error);
      const errorResponse = {
        id: 2,
        type: 'assistant',
        content: { error: 'Failed to fetch ESG intelligence data. Please try again.' },
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorResponse]);
      setIsLoading(false);
    }
  };

  return (
    <motion.div
      className="conversation-view"
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
      transition={{ duration: 0.3 }}
    >
      {/* Header */}
      <div className="conversation-header">
        <motion.button
          className="back-btn"
          onClick={onBack}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <ArrowLeft size={20} />
          Back to Search
        </motion.button>
      </div>

      {/* Messages */}
      <div className="messages-container">
        <AnimatePresence>
          {messages.map((message) => (
            <motion.div
              key={message.id}
              className={`message ${message.type}`}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              {message.type === 'user' ? (
                <div className="user-message">
                  <div className="message-content">{message.content}</div>
                  <div className="message-time">
                    {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                  </div>
                </div>
              ) : (
                <div className="ai-message">
                  <div className="ai-response">
                    {typeof message.content === 'object' ? (
                      message.content.error ? (
                        <div className="error-response">{message.content.error}</div>
                      ) : (
                        <div className="json-response">
                          {message.content.statistics && (
                            <div className="statistics-section">
                              <h3>📊 Intelligence Summary</h3>
                              <div className="stats-grid">
                                <div className="stat-card">
                                  <span className="stat-number">{message.content.statistics.totalArticles}</span>
                                  <span className="stat-label">Articles Found</span>
                                </div>
                                <div className="stat-card">
                                  <span className="stat-number">{message.content.statistics.highImpactCount}</span>
                                  <span className="stat-label">High Impact</span>
                                </div>
                                <div className="stat-card">
                                  <span className="stat-number">{message.content.statistics.opportunityCount}</span>
                                  <span className="stat-label">Opportunities</span>
                                </div>
                                <div className="stat-card">
                                  <span className="stat-number">{message.content.statistics.averageRelevance}%</span>
                                  <span className="stat-label">Avg Relevance</span>
                                </div>
                              </div>
                            </div>
                          )}

                          {message.content.summary && (
                            <div className="summary-section">
                              <h3>Executive Summary</h3>
                              <p>{message.content.summary}</p>
                            </div>
                          )}

                          {message.content.enhancedKeywords && message.content.enhancedKeywords.length > 0 && (
                            <div className="keywords-section">
                              <h3>🎯 Enhanced Search Terms</h3>
                              <div className="keywords-list">
                                {message.content.enhancedKeywords.map((keyword, index) => (
                                  <span key={index} className="keyword-tag">{keyword}</span>
                                ))}
                              </div>
                            </div>
                          )}

                          {message.content.actionItems && message.content.actionItems.length > 0 && (
                            <div className="action-items-section">
                              <h3>⚡ Immediate Action Items</h3>
                              <div className="action-items-list">
                                {message.content.actionItems.map((item, index) => (
                                  <div key={index} className="action-item">
                                    <div className="action-header">
                                      <span className={`priority-badge ${item.priority.toLowerCase()}`}>
                                        {item.priority}
                                      </span>
                                      <span className="department-tag">{item.department}</span>
                                    </div>
                                    <div className="action-text">{item.action}</div>
                                    <div className="action-timeframe">⏱️ {item.timeframe}</div>
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}

                          {message.content.articles && message.content.articles.length > 0 && (
                            <div className="articles-section">
                              <h3>📰 Related Articles ({message.content.articles.length})</h3>
                              <div className="articles-grid">
                                {message.content.articles.map((article, index) => (
                                  <div key={index} className="article-card">
                                    <div className="article-header">
                                      <h4>{article.title}</h4>
                                      <div className="article-badges">
                                        <span className={`impact-badge ${article.impact_level.toLowerCase()}`}>
                                          {article.impact_level}
                                        </span>
                                        <span className="relevance-score">
                                          {article.relevance_score}% relevant
                                        </span>
                                      </div>
                                    </div>
                                    <p className="article-description">{article.description}</p>
                                    <div className="article-meta">
                                      <span className="source">{article.source?.name}</span>
                                      <span className="date">{new Date(article.publishedAt).toLocaleDateString()}</span>
                                    </div>
                                    {article.url && (
                                      <a href={article.url} target="_blank" rel="noopener noreferrer" className="read-more">
                                        Read Full Article
                                      </a>
                                    )}
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}

                          {message.content.analysis && (
                            <div className="analysis-section">
                              <h3>🔍 ESG Analysis</h3>
                              <div className="analysis-content">
                                {Object.entries(message.content.analysis).map(([key, value]) => (
                                  <div key={key} className="analysis-item">
                                    <strong>{key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}:</strong>
                                    <span>{Array.isArray(value) ? value.join(', ') : value}</span>
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      )
                    ) : (
                      <div className="simple-response">{message.content}</div>
                    )}
                  </div>
                  <div className="message-time">
                    {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                  </div>
                </div>
              )}
            </motion.div>
          ))}
        </AnimatePresence>

        {isLoading && (
          <motion.div
            className="loading-message"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
          >
            <div className="loading-content">
              <Loader2 className="loading-spinner" size={20} />
              <span>Analyzing ESG intelligence...</span>
              <div className="loading-steps">
                <div>🎯 Enhancing query with Borouge context</div>
                <div>📰 Searching ESG news sources</div>
                <div>🤖 AI analysis for relevance and impact</div>
                <div>📊 Generating actionable insights</div>
              </div>
            </div>
          </motion.div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* System Status */}
      <div className="system-status-container">
        <div className="system-status-message">
          <p>✅ ESG Intelligence Engine powered by Smart Search API - Ready for real-time ESG data analysis</p>
        </div>
      </div>
    </motion.div>
  );
};

export default ConversationView;
