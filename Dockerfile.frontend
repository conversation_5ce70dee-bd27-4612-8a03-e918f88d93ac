# ============================================================================
# BOROUGE ESG INTELLIGENCE PLATFORM - FRONTEND DOCKERFILE
# ============================================================================
# Multi-stage build for React frontend application

# ============================================================================
# STAGE 1: Base Image with Node.js
# ============================================================================
FROM node:18-alpine AS base

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apk add --no-cache \
    dumb-init \
    curl \
    && rm -rf /var/cache/apk/*

# Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S borouge -u 1001

# ============================================================================
# STAGE 2: Dependencies Installation
# ============================================================================
FROM base AS deps

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production && npm cache clean --force

# ============================================================================
# STAGE 3: Development Stage
# ============================================================================
FROM base AS development

# Copy package files
COPY package*.json ./

# Install all dependencies (including dev dependencies)
RUN npm ci

# Copy source code
COPY . .

# Change ownership to nodejs user
RUN chown -R nodejs:nodejs /app
USER nodejs

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000 || exit 1

# Start development server
CMD ["dumb-init", "npm", "start"]

# ============================================================================
# STAGE 4: Build Stage
# ============================================================================
FROM base AS builder

# Copy package files
COPY package*.json ./

# Install all dependencies
RUN npm ci

# Copy source code
COPY . .

# Build the application
RUN npm run build

# ============================================================================
# STAGE 5: Production Stage with Nginx
# ============================================================================
FROM nginx:alpine AS production

# Install curl for health checks
RUN apk add --no-cache curl

# Copy custom nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

# Copy built application from builder stage
COPY --from=builder /app/build /usr/share/nginx/html

# Create non-root user
RUN addgroup -g 1001 -S nginx && \
    adduser -S nginx -u 1001 -G nginx

# Change ownership of nginx directories
RUN chown -R nginx:nginx /var/cache/nginx && \
    chown -R nginx:nginx /var/log/nginx && \
    chown -R nginx:nginx /etc/nginx/conf.d && \
    touch /var/run/nginx.pid && \
    chown -R nginx:nginx /var/run/nginx.pid

# Switch to non-root user
USER nginx

# Expose port
EXPOSE 80

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:80 || exit 1

# Start nginx
CMD ["nginx", "-g", "daemon off;"]

# ============================================================================
# METADATA
# ============================================================================
LABEL maintainer="Borouge ESG Intelligence Team"
LABEL version="1.0.0"
LABEL description="Borouge ESG Intelligence Platform Frontend"
LABEL org.opencontainers.image.source="https://github.com/Mitty530/Borouge"
LABEL org.opencontainers.image.title="Borouge ESG Frontend"
LABEL org.opencontainers.image.description="Production-ready frontend for Borouge ESG Intelligence Platform"
LABEL org.opencontainers.image.version="1.0.0"
LABEL org.opencontainers.image.vendor="Borouge"
